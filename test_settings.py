#!/usr/bin/env python3
"""
اختبار الإعدادات
"""

import flet as ft

def main(page: ft.Page):
    page.title = "Test Settings"
    page.window_width = 350
    page.window_height = 500
    
    def test_settings(e):
        print("Settings button clicked!")  # للتحقق من أن الدالة تعمل
        
        def close_dialog(e):
            dialog.open = False
            page.update()
        
        def toggle_switch(e):
            print(f"Switch toggled: {e.control.value}")
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Switch is now: {e.control.value}"))
            page.snack_bar.open = True
            page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Test Settings"),
            content=ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.NOTIFICATIONS),
                    title=ft.Text("Notifications"),
                    trailing=ft.Switch(value=True, on_change=toggle_switch)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.LOGIN),
                    title=ft.Text("Auto Login"),
                    trailing=ft.Switch(value=False, on_change=toggle_switch)
                ),
            ], tight=True),
            actions=[
                ft.TextButton("Close", on_click=close_dialog)
            ]
        )
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    def test_language(e):
        print("Language button clicked!")
        page.snack_bar = ft.SnackBar(content=ft.Text("Language menu clicked!"))
        page.snack_bar.open = True
        page.update()
    
    def test_theme(e):
        print("Theme button clicked!")
        page.theme_mode = ft.ThemeMode.DARK if page.theme_mode == ft.ThemeMode.LIGHT else ft.ThemeMode.LIGHT
        page.snack_bar = ft.SnackBar(content=ft.Text("Theme changed!"))
        page.snack_bar.open = True
        page.update()
    
    # إنشاء القائمة
    page.add(
        ft.Column([
            ft.Text("Test Menu", size=24, weight=ft.FontWeight.BOLD),
            ft.Container(height=20),
            
            # قائمة الثلاث نقاط
            ft.Row([
                ft.Container(expand=True),
                ft.PopupMenuButton(
                    icon=ft.Icons.MORE_VERT,
                    items=[
                        ft.PopupMenuItem(
                            text="Settings",
                            icon=ft.Icons.SETTINGS,
                            on_click=test_settings
                        ),
                        ft.PopupMenuItem(
                            text="Language",
                            icon=ft.Icons.LANGUAGE,
                            on_click=test_language
                        ),
                        ft.PopupMenuItem(
                            text="Theme",
                            icon=ft.Icons.PALETTE,
                            on_click=test_theme
                        ),
                    ]
                )
            ], alignment=ft.MainAxisAlignment.END),
            
            ft.Container(height=50),
            ft.Text("Click the ⋮ menu above to test settings", text_align=ft.TextAlign.CENTER),
        ], alignment=ft.MainAxisAlignment.START)
    )

if __name__ == "__main__":
    ft.app(target=main)
