#!/usr/bin/env python3
"""
تطبيق تسجيل دخول مبسط للاختبار
"""

import flet as ft

def main(page: ft.Page):
    page.title = "تسجيل الدخول - Test"
    page.window_width = 400
    page.window_height = 400

    def login_click(e):
        if email.value and password.value:
            result.value = f"مرحباً! {email.value}"
            page.update()
        else:
            result.value = "يرجى ملء جميع الحقول"
            page.update()

    # العناصر
    title = ft.Text("تسجيل الدخول", size=24, weight=ft.FontWeight.BOLD)

    email = ft.TextField(
        label="الإيميل",
        width=300,
        hint_text="أدخل الإيميل"
    )

    password = ft.TextField(
        label="كلمة المرور",
        width=300,
        password=True,
        hint_text="أدخل كلمة المرور"
    )

    login_btn = ft.ElevatedButton(
        "تسجيل الدخول",
        width=300,
        on_click=login_click
    )

    result = ft.Text("", color=ft.Colors.GREEN)

    # إضافة العناصر للصفحة
    page.add(
        ft.Column([
            title,
            ft.Container(height=20),
            email,
            password,
            ft.Container(height=20),
            login_btn,
            result
        ],
        alignment=ft.MainAxisAlignment.CENTER,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    )

if __name__ == "__main__":
    ft.app(target=main)
