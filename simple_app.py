#!/usr/bin/env python3
"""
تطبيق Mürşid مبسط مع إعدادات فعالة
"""

import flet as ft

def main(page: ft.Page):
    page.title = "Mürşid - Simple"
    page.window_width = 350
    page.window_height = 500
    page.window_resizable = True
    
    # إعدادات بسيطة
    def show_settings(e):
        print("Settings clicked!")
        
        def close_dialog(e):
            dialog.open = False
            page.update()
        
        def toggle_notifications(e):
            status = "ON" if e.control.value else "OFF"
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Notifications: {status}"))
            page.snack_bar.open = True
            page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Settings"),
            content=ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.NOTIFICATIONS),
                    title=ft.Text("Notifications"),
                    trailing=ft.Switch(value=True, on_change=toggle_notifications)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.LOGIN),
                    title=ft.Text("Auto Login"),
                    trailing=ft.Switch(value=False, on_change=toggle_notifications)
                ),
            ], tight=True),
            actions=[ft.TextButton("Close", on_click=close_dialog)]
        )
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    # تغيير المظهر
    def show_theme(e):
        print("Theme clicked!")
        
        def close_dialog(e):
            dialog.open = False
            page.update()
        
        def light_theme(e):
            page.theme_mode = ft.ThemeMode.LIGHT
            page.snack_bar = ft.SnackBar(content=ft.Text("Light theme selected"))
            page.snack_bar.open = True
            page.update()
            dialog.open = False
            page.update()
        
        def dark_theme(e):
            page.theme_mode = ft.ThemeMode.DARK
            page.snack_bar = ft.SnackBar(content=ft.Text("Dark theme selected"))
            page.snack_bar.open = True
            page.update()
            dialog.open = False
            page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text("Choose Theme"),
            content=ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.LIGHT_MODE),
                    title=ft.Text("Light Theme"),
                    on_click=light_theme
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.Icons.DARK_MODE),
                    title=ft.Text("Dark Theme"),
                    on_click=dark_theme
                ),
            ], tight=True),
            actions=[ft.TextButton("Cancel", on_click=close_dialog)]
        )
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    # تغيير اللغة
    def show_language(e):
        print("Language clicked!")
        page.snack_bar = ft.SnackBar(content=ft.Text("Language options: TR, AR, EN"))
        page.snack_bar.open = True
        page.update()
    
    # تسجيل الدخول
    def login_clicked(e):
        if email_field.value and password_field.value:
            page.snack_bar = ft.SnackBar(content=ft.Text(f"Welcome! {email_field.value}"))
            page.snack_bar.open = True
            page.update()
        else:
            page.snack_bar = ft.SnackBar(content=ft.Text("Please fill all fields"))
            page.snack_bar.open = True
            page.update()
    
    # العناصر
    email_field = ft.TextField(label="Email", hint_text="<EMAIL>", expand=True)
    password_field = ft.TextField(label="Password", password=True, expand=True)
    login_button = ft.ElevatedButton("Login", expand=True, on_click=login_clicked)
    
    # التخطيط
    page.add(
        ft.Container(
            content=ft.Column([
                # قائمة الإعدادات
                ft.Row([
                    ft.Container(expand=True),
                    ft.PopupMenuButton(
                        icon=ft.Icons.MORE_VERT,
                        items=[
                            ft.PopupMenuItem(text="Settings", icon=ft.Icons.SETTINGS, on_click=show_settings),
                            ft.PopupMenuItem(text="Theme", icon=ft.Icons.PALETTE, on_click=show_theme),
                            ft.PopupMenuItem(text="Language", icon=ft.Icons.LANGUAGE, on_click=show_language),
                        ]
                    )
                ], alignment=ft.MainAxisAlignment.END),
                
                # اللوجو والعنوان
                ft.Text("🛡️", size=60, text_align=ft.TextAlign.CENTER),
                ft.Text("Mürşid", size=24, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                ft.Text("Login", size=18, text_align=ft.TextAlign.CENTER),
                ft.Container(height=20),
                
                # حقول الإدخال
                ft.ResponsiveRow([
                    ft.Column([
                        email_field,
                        ft.Container(height=10),
                        password_field,
                        ft.Container(height=20),
                        login_button,
                    ], col=12, alignment=ft.MainAxisAlignment.CENTER)
                ]),
                
            ], alignment=ft.MainAxisAlignment.START, spacing=10),
            padding=20,
            expand=True
        )
    )

if __name__ == "__main__":
    ft.app(target=main)
